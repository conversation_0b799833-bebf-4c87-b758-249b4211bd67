"""
Quick Database Size Checker

This script quickly checks how many documents are in your Cosmos DB
without loading all the data.
"""

import os
import logging
from dotenv import load_dotenv
from azure.cosmos import CosmosClient

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def check_database_size():
    """Check the size of the database without loading all data."""
    
    try:
        # Setup Cosmos client
        cosmos_endpoint = os.getenv('COSMOS_ENDPOINT')
        cosmos_key = os.getenv('COSMOS_KEY')
        database_name = os.getenv('DATABASE_NAME', 'cosmicworks')
        container_name = os.getenv('CONTAINER_NAME', 'products')
        
        cosmos_client = CosmosClient(cosmos_endpoint, cosmos_key)
        database = cosmos_client.get_database_client(database_name)
        container = database.get_container_client(container_name)
        
        # Count documents using aggregation (much faster)
        count_query = "SELECT VALUE COUNT(1) FROM c"
        
        logger.info("🔍 Counting documents in database...")
        
        result = list(container.query_items(
            query=count_query,
            enable_cross_partition_query=True
        ))
        
        total_count = result[0] if result else 0
        
        print(f"📊 Database Statistics:")
        print(f"Database: {database_name}")
        print(f"Container: {container_name}")
        print(f"Total Documents: {total_count:,}")
        
        if total_count > 10000:
            print("⚠️  WARNING: Large dataset detected!")
            print("   This explains why your search is taking so long.")
            print("   Consider using the optimized search with limits.")
        elif total_count > 1000:
            print("ℹ️  Medium dataset - search should be reasonable")
        else:
            print("✅ Small dataset - search should be fast")
        
        # Sample a few documents to check structure
        sample_query = "SELECT TOP 3 c.id, c.chunk_index FROM c"
        samples = list(container.query_items(
            query=sample_query,
            enable_cross_partition_query=True
        ))
        
        print(f"\n📋 Sample Documents:")
        for i, doc in enumerate(samples, 1):
            print(f"  {i}. ID: {doc.get('id', 'N/A')}, Chunk: {doc.get('chunk_index', 'N/A')}")
        
        return total_count
        
    except Exception as e:
        logger.error(f"❌ Failed to check database size: {e}")
        return None


def main():
    """Main function."""
    print("🔍 Mastercard Priceless Database Size Checker")
    print("=" * 50)
    
    count = check_database_size()
    
    if count is not None:
        print(f"\n✅ Check completed successfully!")
        
        if count > 5000:
            print("\n💡 Recommendations for large datasets:")
            print("1. Use the optimized search with document limits")
            print("2. Consider implementing vector indexing")
            print("3. Use batch processing for better performance")
            print("\nTo run optimized search:")
            print("python search_retrieval.py --mode single --query 'your query' --top-k 5")
    else:
        print("\n❌ Check failed. Please verify your credentials.")


if __name__ == "__main__":
    main()
