"""
<PERSON><PERSON> and <PERSON><PERSON> - Simplified Version

This version creates simplified chunks with only the 'content' field,
removing chunk_id, chunk_type, product_id, text, and metadata fields.

Based on the original flatten_and_chunk.py but with streamlined output.
"""

import json
import uuid
from typing import Dict, List, Any

def flatten_json(nested_json: Dict, prefix: str = "", separator: str = "_") -> Dict:
    """
    Flatten a nested JSON object into a flat dictionary with concatenated keys.

    Args:
        nested_json: The nested JSON object to flatten
        prefix: Prefix for the flattened keys
        separator: Separator to use between key parts

    Returns:
        A flattened dictionary
    """
    flattened = {}

    for key, value in nested_json.items():
        new_key = f"{prefix}{separator}{key}" if prefix else key

        if isinstance(value, dict):
            # Recursively flatten nested dictionaries
            flattened.update(flatten_json(value, new_key, separator))
        elif isinstance(value, list) and len(value) > 0 and isinstance(value[0], dict):
            # Handle lists of dictionaries
            for i, item in enumerate(value):
                flattened.update(flatten_json(item, f"{new_key}{separator}{i}", separator))
        elif isinstance(value, list) and len(value) > 0 and isinstance(value[0], (str, int, float, bool)):
            # Join simple lists into strings
            flattened[new_key] = ", ".join(str(item) for item in value)
        else:
            # Add simple values directly
            flattened[new_key] = value

    return flattened

def process_rag_data_simplified(input_file: str, output_file: str):
    """
    Process RAG data by flattening and creating simplified chunks with only content field.

    Args:
        input_file: Path to the input JSON file
        output_file: Path to the output JSON file
    """
    # Load the input JSON data
    with open(input_file, 'r', encoding='utf-8') as f:
        data = json.load(f)

    products = data.get("products", [])
    chunks = []

    # Create simplified product-level chunks
    for product in products:
        # Flatten the product
        flattened_product = flatten_json(product)

        # Create a simplified chunk with only content
        chunk = {
            "content": flattened_product
        }

        chunks.append(chunk)

    # Create the simplified output data
    output_data = {
        "chunks": chunks,
        "metadata": {
            "total_chunks": len(chunks),
            "chunking_strategy": "product_simplified",
            "version": "simplified",
            "description": "Simplified chunks with only content field",
            "removed_fields": ["chunk_id", "chunk_type", "product_id", "text", "metadata"],
            "source": data.get("metadata", {}).get("source", ""),
            "generated_at": data.get("metadata", {}).get("generated_at", "")
        }
    }

    # Save the output JSON data
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(output_data, f, indent=2, ensure_ascii=False)

    print(f"✅ Successfully processed {len(products)} products into {len(chunks)} simplified chunks.")
    print(f"❌ Removed fields: chunk_id, chunk_type, product_id, text, metadata")
    print(f"✅ Kept only: content field with flattened product data")
    print(f"📁 Output saved to {output_file}")

def create_jsonl_output(input_file: str, output_file: str):
    """
    Create JSONL output with only content field for each product.
    
    Args:
        input_file: Path to the input JSON file
        output_file: Path to the output JSONL file
    """
    # Load the input JSON data
    with open(input_file, 'r', encoding='utf-8') as f:
        data = json.load(f)

    products = data.get("products", [])
    
    # Create JSONL output
    with open(output_file, 'w', encoding='utf-8') as f:
        for product in products:
            # Flatten the product
            flattened_product = flatten_json(product)
            
            # Create simplified record with only content
            record = {
                "content": flattened_product
            }
            
            # Write as JSONL (one JSON object per line)
            f.write(json.dumps(record, ensure_ascii=False) + '\n')

    print(f"✅ Successfully created JSONL output with {len(products)} records.")
    print(f"📋 Each record contains only the 'content' field with flattened product data.")
    print(f"📁 JSONL output saved to {output_file}")

def create_content_only_output(input_file: str, output_file: str):
    """
    Create output with just the flattened content (no wrapper).
    
    Args:
        input_file: Path to the input JSON file
        output_file: Path to the output JSON file
    """
    # Load the input JSON data
    with open(input_file, 'r', encoding='utf-8') as f:
        data = json.load(f)

    products = data.get("products", [])
    content_list = []

    # Create list of flattened content only
    for product in products:
        # Flatten the product and add directly to list
        flattened_product = flatten_json(product)
        content_list.append(flattened_product)

    # Save as simple array of flattened products
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(content_list, f, indent=2, ensure_ascii=False)

    print(f"✅ Successfully created content-only output with {len(content_list)} flattened products.")
    print(f"📋 Output is a simple array of flattened product objects.")
    print(f"📁 Content-only output saved to {output_file}")

def main():
    """Main function to run the script."""
    import argparse

    parser = argparse.ArgumentParser(description="Process RAG data with simplified output")
    parser.add_argument("--input", type=str, default="rag_optimized_product.json", help="Input file path")
    parser.add_argument("--output", type=str, default="rag_chunked_simplified.json", help="Output JSON file path")
    parser.add_argument("--jsonl", type=str, help="Optional JSONL output file path")
    parser.add_argument("--content-only", type=str, help="Optional content-only JSON output file path")
    parser.add_argument("--format", choices=["json", "jsonl", "content-only", "all"], default="json", 
                       help="Output format: json, jsonl, content-only, or all")

    args = parser.parse_args()

    print("🚀 Starting simplified RAG data processing...")
    print(f"📥 Input file: {args.input}")
    print(f"🎯 Format: {args.format}")
    print("-" * 50)

    if args.format in ["json", "all"]:
        print("📊 Creating simplified JSON output...")
        process_rag_data_simplified(args.input, args.output)
        print()
    
    if args.format in ["jsonl", "all"]:
        print("📄 Creating JSONL output...")
        jsonl_output = args.jsonl or args.output.replace('.json', '.jsonl')
        create_jsonl_output(args.input, jsonl_output)
        print()

    if args.format in ["content-only", "all"]:
        print("📋 Creating content-only output...")
        content_output = args.content_only or args.output.replace('.json', '_content_only.json')
        create_content_only_output(args.input, content_output)
        print()

    print("🎉 Processing completed successfully!")

if __name__ == "__main__":
    main()
