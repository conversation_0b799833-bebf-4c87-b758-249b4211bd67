"""
RAG Optimized Flatten Script

This script takes the nested structure from rag_optimized_product.json and applies
the flattening logic from flatten_and_chunk_simplified.py to create a flattened
output with only content fields.

Input: rag_optimized_product.json (nested structure)
Output: Flattened structure similar to rag_chunked_simplified_content_only.json
"""

import json
from typing import Dict, List, Any

def flatten_json(nested_json: Dict, prefix: str = "", separator: str = "_") -> Dict:
    """
    Flatten a nested JSON object into a flat dictionary with concatenated keys.

    Args:
        nested_json: The nested JSON object to flatten
        prefix: Prefix for the flattened keys
        separator: Separator to use between key parts

    Returns:
        A flattened dictionary
    """
    flattened = {}

    for key, value in nested_json.items():
        new_key = f"{prefix}{separator}{key}" if prefix else key

        if isinstance(value, dict):
            # Recursively flatten nested dictionaries
            flattened.update(flatten_json(value, new_key, separator))
        elif isinstance(value, list) and len(value) > 0 and isinstance(value[0], dict):
            # Handle lists of dictionaries
            for i, item in enumerate(value):
                flattened.update(flatten_json(item, f"{new_key}{separator}{i}", separator))
        elif isinstance(value, list) and len(value) > 0 and isinstance(value[0], (str, int, float, bool)):
            # Join simple lists into strings
            flattened[new_key] = ", ".join(str(item) for item in value)
        else:
            # Add simple values directly
            flattened[new_key] = value

    return flattened

def process_rag_optimized_data(input_file: str, output_file: str):
    """
    Process RAG optimized data by flattening the nested structure.

    Args:
        input_file: Path to the input JSON file (rag_optimized_product.json)
        output_file: Path to the output JSON file
    """
    # Load the input JSON data
    with open(input_file, 'r', encoding='utf-8') as f:
        data = json.load(f)

    products = data.get("products", [])
    flattened_products = []

    # Process each product
    for product in products:
        # Flatten the product using the same logic as flatten_and_chunk_simplified.py
        flattened_product = flatten_json(product)
        
        # Add the flattened product directly to the list (content-only format)
        flattened_products.append(flattened_product)

    # Save as simple array of flattened products (same format as rag_chunked_simplified_content_only.json)
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(flattened_products, f, indent=2, ensure_ascii=False)

    print(f"✅ Successfully processed {len(products)} products from RAG optimized format.")
    print(f"📋 Applied flattening logic to nested structure.")
    print(f"📁 Output saved to {output_file}")

def create_chunked_format(input_file: str, output_file: str):
    """
    Create chunked format with content wrapper (similar to rag_chunked_simplified.json).

    Args:
        input_file: Path to the input JSON file (rag_optimized_product.json)
        output_file: Path to the output JSON file
    """
    # Load the input JSON data
    with open(input_file, 'r', encoding='utf-8') as f:
        data = json.load(f)

    products = data.get("products", [])
    chunks = []

    # Create chunks with content wrapper
    for product in products:
        # Flatten the product
        flattened_product = flatten_json(product)

        # Create chunk with content wrapper
        chunk = {
            "content": flattened_product
        }

        chunks.append(chunk)

    # Create the output data with metadata
    output_data = {
        "chunks": chunks,
        "metadata": {
            "total_chunks": len(chunks),
            "chunking_strategy": "rag_optimized_flattened",
            "version": "rag_optimized_flatten",
            "description": "Flattened RAG optimized products with content wrapper",
            "source": data.get("metadata", {}).get("source", ""),
            "generated_at": data.get("metadata", {}).get("generated_at", "")
        }
    }

    # Save the output JSON data
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(output_data, f, indent=2, ensure_ascii=False)

    print(f"✅ Successfully created chunked format with {len(chunks)} chunks.")
    print(f"📋 Each chunk contains flattened RAG optimized product data.")
    print(f"📁 Chunked output saved to {output_file}")

def create_jsonl_output(input_file: str, output_file: str):
    """
    Create JSONL output from RAG optimized data.
    
    Args:
        input_file: Path to the input JSON file (rag_optimized_product.json)
        output_file: Path to the output JSONL file
    """
    # Load the input JSON data
    with open(input_file, 'r', encoding='utf-8') as f:
        data = json.load(f)

    products = data.get("products", [])
    
    # Create JSONL output
    with open(output_file, 'w', encoding='utf-8') as f:
        for product in products:
            # Flatten the product
            flattened_product = flatten_json(product)
            
            # Create record with content wrapper
            record = {
                "content": flattened_product
            }
            
            # Write as JSONL (one JSON object per line)
            f.write(json.dumps(record, ensure_ascii=False) + '\n')

    print(f"✅ Successfully created JSONL output with {len(products)} records.")
    print(f"📋 Each record contains flattened RAG optimized product data.")
    print(f"📁 JSONL output saved to {output_file}")

def main():
    """Main function to run the script."""
    import argparse

    parser = argparse.ArgumentParser(description="Flatten RAG optimized product data")
    parser.add_argument("--input", type=str, default="rag_optimized_product.json", help="Input file path")
    parser.add_argument("--output", type=str, default="rag_optimized_flattened.json", help="Output JSON file path")
    parser.add_argument("--chunked", type=str, help="Optional chunked format output file path")
    parser.add_argument("--jsonl", type=str, help="Optional JSONL output file path")
    parser.add_argument("--format", choices=["content-only", "chunked", "jsonl", "all"], default="content-only", 
                       help="Output format: content-only, chunked, jsonl, or all")

    args = parser.parse_args()

    print("🚀 Starting RAG optimized data flattening...")
    print(f"📥 Input file: {args.input}")
    print(f"🎯 Format: {args.format}")
    print("-" * 50)

    if args.format in ["content-only", "all"]:
        print("📋 Creating content-only flattened output...")
        process_rag_optimized_data(args.input, args.output)
        print()

    if args.format in ["chunked", "all"]:
        print("📊 Creating chunked format output...")
        chunked_output = args.chunked or args.output.replace('.json', '_chunked.json')
        create_chunked_format(args.input, chunked_output)
        print()
    
    if args.format in ["jsonl", "all"]:
        print("📄 Creating JSONL output...")
        jsonl_output = args.jsonl or args.output.replace('.json', '.jsonl')
        create_jsonl_output(args.input, jsonl_output)
        print()

    print("🎉 RAG optimized flattening completed successfully!")

if __name__ == "__main__":
    main()
