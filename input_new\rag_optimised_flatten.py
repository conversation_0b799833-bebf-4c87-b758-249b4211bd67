"""
Simple RAG Flatten Script
Converts rag_optimized_product.json to rag_optimized_flattened.json
"""

import json

def flatten_json(nested_json, prefix="", separator="_"):
    """Flatten nested JSON into flat dictionary with concatenated keys."""
    flattened = {}

    for key, value in nested_json.items():
        new_key = f"{prefix}{separator}{key}" if prefix else key

        if isinstance(value, dict):
            flattened.update(flatten_json(value, new_key, separator))
        elif isinstance(value, list) and len(value) > 0 and isinstance(value[0], dict):
            for i, item in enumerate(value):
                flattened.update(flatten_json(item, f"{new_key}{separator}{i}", separator))
        elif isinstance(value, list) and len(value) > 0:
            flattened[new_key] = ", ".join(str(item) for item in value)
        else:
            flattened[new_key] = value

    return flattened

def convert_rag_to_flattened():
    """Convert rag_optimized_product.json to rag_optimized_flattened.json"""

    # Load input file
    with open('input_new/rag_optimized_product.json', 'r', encoding='utf-8') as f:
        data = json.load(f)

    # Flatten each product
    flattened_products = []
    for product in data.get("products", []):
        flattened_product = flatten_json(product)
        flattened_products.append(flattened_product)

    # Save output file
    with open('input_new/rag_optimized_flattened.json', 'w', encoding='utf-8') as f:
        json.dump(flattened_products, f, indent=2, ensure_ascii=False)

    print(f"Converted {len(flattened_products)} products to flattened format")

if __name__ == "__main__":
    convert_rag_to_flattened()
