"""
Cosmos DB Container Setup Script for Serverless Accounts

This script helps set up the required container for serverless Cosmos DB accounts.
Run this before running the main data processor.
"""

import os
import logging
from dotenv import load_dotenv
from azure.cosmos import CosmosClient, PartitionKey
from azure.cosmos.exceptions import CosmosResourceExistsError

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def setup_cosmos_container():
    """Set up Cosmos DB container for serverless account."""
    
    # Load configuration
    cosmos_endpoint = os.getenv('COSMOS_ENDPOINT')
    cosmos_key = os.getenv('COSMOS_KEY')
    database_name = os.getenv('DATABASE_NAME', 'cosmicworks')
    container_name = os.getenv('CONTAINER_NAME', 'products')
    
    if not cosmos_endpoint or not cosmos_key:
        logger.error("❌ Missing COSMOS_ENDPOINT or COSMOS_KEY in .env file")
        return False
    
    try:
        # Initialize Cosmos client
        logger.info("🔗 Connecting to Cosmos DB...")
        cosmos_client = CosmosClient(cosmos_endpoint, cosmos_key)
        
        # Create or get database
        try:
            database = cosmos_client.create_database(database_name)
            logger.info(f"✅ Created database: {database_name}")
        except CosmosResourceExistsError:
            database = cosmos_client.get_database_client(database_name)
            logger.info(f"✅ Using existing database: {database_name}")
        
        # Create or get container (serverless - no throughput)
        try:
            container = database.create_container(
                id=container_name,
                partition_key=PartitionKey(path="/id")
            )
            logger.info(f"✅ Created container: {container_name} (serverless)")
        except CosmosResourceExistsError:
            container = database.get_container_client(container_name)
            logger.info(f"✅ Using existing container: {container_name}")
        
        # Test container access
        logger.info("🧪 Testing container access...")
        
        # Try to query the container
        items = list(container.query_items(
            query="SELECT * FROM c",
            enable_cross_partition_query=True,
            max_item_count=1
        ))
        
        logger.info(f"✅ Container access successful. Found {len(items)} existing items.")
        
        # Test write access with a simple document
        test_doc = {
            "id": "setup_test",
            "test": True,
            "message": "Container setup test"
        }
        
        container.upsert_item(test_doc)
        logger.info("✅ Write access successful")
        
        # Clean up test document
        container.delete_item(item="setup_test", partition_key="setup_test")
        logger.info("✅ Test document cleaned up")
        
        logger.info("🎉 Cosmos DB container setup completed successfully!")
        logger.info(f"Database: {database_name}")
        logger.info(f"Container: {container_name}")
        logger.info("You can now run: python data_processor.py")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Setup failed: {e}")
        return False


def main():
    """Main function."""
    print("🚀 Cosmos DB Container Setup for Serverless Accounts")
    print("=" * 55)
    
    if setup_cosmos_container():
        print("\n✅ Setup completed successfully!")
        print("\nNext steps:")
        print("1. Run: python test_credentials.py")
        print("2. Run: python data_processor.py")
    else:
        print("\n❌ Setup failed. Please check the errors above.")


if __name__ == "__main__":
    main()
