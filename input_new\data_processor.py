"""
Mastercard Priceless Data Processing and Embedding Module

This module handles:
1. Loading and flattening JSON product data
2. Chunking data for optimal embedding
3. Generating embeddings using Azure OpenAI
4. Storing embeddings in Azure Cosmos DB

Author: Mastercard Team
Version: 1.0
"""

import json
import os
import logging
from typing import List, Dict, Any, Optional
from dotenv import load_dotenv
import openai
from azure.cosmos import CosmosClient, PartitionKey
from azure.cosmos.exceptions import CosmosResourceExistsError

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class DataProcessor:
    """Handles data processing, embedding generation, and storage operations."""

    def __init__(self):
        """Initialize the DataProcessor with configuration from environment variables."""
        self._load_config()
        self._setup_openai()
        self._setup_cosmos_client()

    def _load_config(self):
        """Load configuration from environment variables."""
        # Azure OpenAI Configuration
        self.openai_api_key = os.getenv('OPENAI_API_KEY')
        self.openai_api_base = os.getenv('AZURE_EXISTING_AIPROJECT_ENDPOINT')
        self.openai_api_version = "2023-05-15"
        self.embedding_deployment = "ada-embedding"

        # Azure Cosmos DB Configuration
        self.cosmos_endpoint = os.getenv('COSMOS_ENDPOINT')
        self.cosmos_key = os.getenv('COSMOS_KEY')
        self.database_name = os.getenv('DATABASE_NAME', 'cosmicworks')
        self.container_name = os.getenv('CONTAINER_NAME', 'products')

        # Validate required environment variables
        required_vars = [
            'OPENAI_API_KEY', 'AZURE_EXISTING_AIPROJECT_ENDPOINT',
            'COSMOS_ENDPOINT', 'COSMOS_KEY'
        ]

        missing_vars = [var for var in required_vars if not os.getenv(var)]
        if missing_vars:
            raise ValueError(f"Missing required environment variables: {missing_vars}")

    def _setup_openai(self):
        """Configure OpenAI client for Azure."""
        openai.api_type = "azure"
        openai.api_base = self.openai_api_base
        openai.api_key = self.openai_api_key
        openai.api_version = self.openai_api_version
        logger.info("OpenAI client configured successfully")

    def _setup_cosmos_client(self):
        """Initialize Cosmos DB client and ensure database/container exist."""
        try:
            self.cosmos_client = CosmosClient(self.cosmos_endpoint, self.cosmos_key)

            # Create database if it doesn't exist
            try:
                self.database = self.cosmos_client.create_database(self.database_name)
                logger.info(f"Created database: {self.database_name}")
            except CosmosResourceExistsError:
                self.database = self.cosmos_client.get_database_client(self.database_name)
                logger.info(f"Using existing database: {self.database_name}")

            # Create container if it doesn't exist
            try:
                # Try creating container without throughput first (for serverless accounts)
                self.container = self.database.create_container(
                    id=self.container_name,
                    partition_key=PartitionKey(path="/id")
                )
                logger.info(f"Created container: {self.container_name} (serverless)")
            except CosmosResourceExistsError:
                self.container = self.database.get_container_client(self.container_name)
                logger.info(f"Using existing container: {self.container_name}")
            except Exception as e:
                # If serverless creation fails, try with throughput (for provisioned accounts)
                if "serverless" in str(e).lower():
                    logger.info("Serverless account detected, creating container without throughput")
                    try:
                        self.container = self.database.create_container(
                            id=self.container_name,
                            partition_key=PartitionKey(path="/id")
                        )
                        logger.info(f"Created container: {self.container_name} (serverless)")
                    except CosmosResourceExistsError:
                        self.container = self.database.get_container_client(self.container_name)
                        logger.info(f"Using existing container: {self.container_name}")
                else:
                    # Try with throughput for provisioned accounts
                    try:
                        self.container = self.database.create_container(
                            id=self.container_name,
                            partition_key=PartitionKey(path="/id"),
                            offer_throughput=400
                        )
                        logger.info(f"Created container: {self.container_name} (provisioned)")
                    except CosmosResourceExistsError:
                        self.container = self.database.get_container_client(self.container_name)
                        logger.info(f"Using existing container: {self.container_name}")
                    except Exception as inner_e:
                        logger.error(f"Failed to create container: {inner_e}")
                        raise

        except Exception as e:
            logger.error(f"Failed to setup Cosmos DB client: {e}")
            raise

    def load_json_data(self, file_path: str) -> List[Dict[str, Any]]:
        """
        Load JSON data from file.

        Args:
            file_path (str): Path to the JSON file

        Returns:
            List[Dict[str, Any]]: Loaded JSON data
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                data = json.load(file)
            logger.info(f"Successfully loaded {len(data)} items from {file_path}")
            return data
        except Exception as e:
            logger.error(f"Failed to load JSON data from {file_path}: {e}")
            raise

    def flatten_data(self, data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Flatten nested JSON data structures.

        Args:
            data (List[Dict[str, Any]]): Original nested data

        Returns:
            List[Dict[str, Any]]: Flattened data
        """
        def flatten_dict(d: Dict[str, Any], parent_key: str = '', sep: str = '_') -> Dict[str, Any]:
            """Recursively flatten a nested dictionary."""
            items = []
            for k, v in d.items():
                new_key = f"{parent_key}{sep}{k}" if parent_key else k
                if isinstance(v, dict):
                    items.extend(flatten_dict(v, new_key, sep=sep).items())
                elif isinstance(v, list):
                    for i, item in enumerate(v):
                        if isinstance(item, dict):
                            items.extend(flatten_dict(item, f"{new_key}_{i}", sep=sep).items())
                        else:
                            items.append((f"{new_key}_{i}", item))
                else:
                    items.append((new_key, v))
            return dict(items)

        flattened_data = []
        for item in data:
            if isinstance(item, dict):
                flattened_data.append(flatten_dict(item))
            else:
                logger.warning(f"Skipping non-dictionary item: {item}")

        logger.info(f"Flattened {len(flattened_data)} items")
        return flattened_data

    def chunk_data(self, item: Dict[str, Any], max_chunk_size: int = 8000) -> List[Dict[str, Any]]:
        """
        Chunk a single data item if it's too large for embedding.

        Args:
            item (Dict[str, Any]): Data item to chunk
            max_chunk_size (int): Maximum size per chunk in characters

        Returns:
            List[Dict[str, Any]]: List of chunks
        """
        item_str = json.dumps(item)

        if len(item_str) <= max_chunk_size:
            return [item]

        # If item is too large, split into chunks
        chunks = []
        keys = list(item.keys())
        current_chunk = {}
        current_size = 0

        for key in keys:
            key_value_str = json.dumps({key: item[key]})

            if current_size + len(key_value_str) > max_chunk_size and current_chunk:
                chunks.append(current_chunk.copy())
                current_chunk = {}
                current_size = 0

            current_chunk[key] = item[key]
            current_size += len(key_value_str)

        if current_chunk:
            chunks.append(current_chunk)

        return chunks

    def process_data_for_embedding(self, data: List[Dict[str, Any]]) -> List[str]:
        """
        Process and chunk data, converting to strings for embedding.

        Args:
            data (List[Dict[str, Any]]): Flattened data

        Returns:
            List[str]: List of string chunks ready for embedding
        """
        all_chunks = []

        for item in data:
            chunks = self.chunk_data(item)
            for chunk in chunks:
                all_chunks.append(json.dumps(chunk))

        logger.info(f"Created {len(all_chunks)} chunks for embedding")
        return all_chunks

    def generate_embeddings(self, text_chunks: List[str], batch_size: int = 16) -> List[List[float]]:
        """
        Generate embeddings for text chunks using Azure OpenAI.

        Args:
            text_chunks (List[str]): List of text chunks
            batch_size (int): Number of chunks to process in each batch

        Returns:
            List[List[float]]: List of embedding vectors
        """
        all_embeddings = []

        for i in range(0, len(text_chunks), batch_size):
            batch = text_chunks[i:i + batch_size]

            try:
                response = openai.Embedding.create(
                    input=batch,
                    engine=self.embedding_deployment
                )

                batch_embeddings = [item['embedding'] for item in response['data']]
                all_embeddings.extend(batch_embeddings)

                logger.info(f"Generated embeddings for batch {i//batch_size + 1}/{(len(text_chunks)-1)//batch_size + 1}")

            except Exception as e:
                logger.error(f"Failed to generate embeddings for batch {i//batch_size + 1}: {e}")
                raise

        logger.info(f"Successfully generated {len(all_embeddings)} embeddings")
        return all_embeddings

    def store_embeddings(self, text_chunks: List[str], embeddings: List[List[float]]) -> None:
        """
        Store text chunks and their embeddings in Cosmos DB.

        Args:
            text_chunks (List[str]): Original text chunks
            embeddings (List[List[float]]): Corresponding embedding vectors
        """
        if len(text_chunks) != len(embeddings):
            raise ValueError("Number of text chunks must match number of embeddings")

        stored_count = 0

        for i, (text_chunk, embedding) in enumerate(zip(text_chunks, embeddings)):
            try:
                # Parse the JSON string back to dict for better storage
                chunk_data = json.loads(text_chunk)

                document = {
                    "id": f"chunk_{i}",
                    "data": chunk_data,
                    "text": text_chunk,
                    "embedding": embedding,
                    "chunk_index": i
                }

                self.container.create_item(body=document)
                stored_count += 1

                if stored_count % 10 == 0:
                    logger.info(f"Stored {stored_count}/{len(text_chunks)} documents")

            except Exception as e:
                logger.error(f"Failed to store document {i}: {e}")
                continue

        logger.info(f"Successfully stored {stored_count} documents in Cosmos DB")

    def process_and_store_data(self, json_file_path: str) -> None:
        """
        Complete pipeline: load, process, embed, and store data.

        Args:
            json_file_path (str): Path to the JSON data file
        """
        logger.info("Starting data processing pipeline...")

        # Load and process data
        raw_data = self.load_json_data(json_file_path)
        flattened_data = self.flatten_data(raw_data)
        text_chunks = self.process_data_for_embedding(flattened_data)

        # Generate embeddings
        embeddings = self.generate_embeddings(text_chunks)

        # Store in Cosmos DB
        self.store_embeddings(text_chunks, embeddings)

        logger.info("Data processing pipeline completed successfully!")


def main():
    """Main function to run the data processing pipeline."""
    try:
        # Initialize processor
        processor = DataProcessor()

        # Process data (update path as needed)
        json_file_path = "products_only.json"  # Update this path
        processor.process_and_store_data(json_file_path)

    except Exception as e:
        logger.error(f"Pipeline failed: {e}")
        raise


if __name__ == "__main__":
    main()
