"""
Mastercard Priceless Search and Retrieval Module

This module handles:
1. <PERSON>se vector search using cosine similarity
2. Retrieving relevant documents from Azure Cosmos DB
3. Generating responses using Azure OpenAI GPT models
4. RAG (Retrieval-Augmented Generation) pipeline

Author: Mastercard Team
Version: 1.0
"""

import json
import os
import logging
from typing import List, Dict, Any, Tuple, Optional
from dotenv import load_dotenv
import numpy as np
import openai
from azure.cosmos import CosmosClient

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class SearchRetrieval:
    """Handles search, retrieval, and response generation operations."""

    def __init__(self):
        """Initialize the SearchRetrieval with configuration from environment variables."""
        self._load_config()
        self._setup_openai()
        self._setup_cosmos_client()

    def _load_config(self):
        """Load configuration from environment variables."""
        # Azure OpenAI Configuration
        self.openai_api_key = os.getenv('OPENAI_API_KEY')
        self.openai_api_base = os.getenv('AZURE_EXISTING_AIPROJECT_ENDPOINT')
        self.openai_api_version = "2023-05-15"
        self.embedding_deployment = "ada-embedding"
        self.gpt_deployment = "gpt-4o-mini"

        # Azure Cosmos DB Configuration
        self.cosmos_endpoint = os.getenv('COSMOS_ENDPOINT')
        self.cosmos_key = os.getenv('COSMOS_KEY')
        self.database_name = os.getenv('DATABASE_NAME', 'cosmicworks')
        self.container_name = os.getenv('CONTAINER_NAME', 'products')

        # Validate required environment variables
        required_vars = [
            'OPENAI_API_KEY', 'AZURE_EXISTING_AIPROJECT_ENDPOINT',
            'COSMOS_ENDPOINT', 'COSMOS_KEY'
        ]

        missing_vars = [var for var in required_vars if not os.getenv(var)]
        if missing_vars:
            raise ValueError(f"Missing required environment variables: {missing_vars}")

    def _setup_openai(self):
        """Configure OpenAI client for Azure."""
        openai.api_type = "azure"
        openai.api_base = self.openai_api_base
        openai.api_key = self.openai_api_key
        openai.api_version = self.openai_api_version
        logger.info("OpenAI client configured successfully")

    def _setup_cosmos_client(self):
        """Initialize Cosmos DB client."""
        try:
            self.cosmos_client = CosmosClient(self.cosmos_endpoint, self.cosmos_key)
            self.database = self.cosmos_client.get_database_client(self.database_name)
            self.container = self.database.get_container_client(self.container_name)
            logger.info("Cosmos DB client configured successfully")
        except Exception as e:
            logger.error(f"Failed to setup Cosmos DB client: {e}")
            raise

    def cosine_similarity(self, vector_a: List[float], vector_b: List[float]) -> float:
        """
        Calculate cosine similarity between two vectors.

        Args:
            vector_a (List[float]): First vector
            vector_b (List[float]): Second vector

        Returns:
            float: Cosine similarity score
        """
        a = np.array(vector_a)
        b = np.array(vector_b)

        # Handle zero vectors
        norm_a = np.linalg.norm(a)
        norm_b = np.linalg.norm(b)

        if norm_a == 0 or norm_b == 0:
            return 0.0

        return np.dot(a, b) / (norm_a * norm_b)

    def get_embedding(self, text: str) -> List[float]:
        """
        Generate embedding for a given text using Azure OpenAI.

        Args:
            text (str): Input text to embed

        Returns:
            List[float]: Embedding vector
        """
        try:
            response = openai.Embedding.create(
                input=text,
                engine=self.embedding_deployment
            )
            return response["data"][0]["embedding"]
        except Exception as e:
            logger.error(f"Failed to generate embedding: {e}")
            raise

    def get_all_documents(self, max_items: int = 1000) -> List[Dict[str, Any]]:
        """
        Retrieve documents from Cosmos DB with pagination limit.

        Args:
            max_items (int): Maximum number of items to retrieve

        Returns:
            List[Dict[str, Any]]: List of documents (limited)
        """
        try:
            # Use query with limit instead of read_all_items for better performance
            query = "SELECT * FROM c"
            documents = list(self.container.query_items(
                query=query,
                enable_cross_partition_query=True,
                max_item_count=max_items
            ))
            logger.info(f"Retrieved {len(documents)} documents from Cosmos DB (limited to {max_items})")
            return documents
        except Exception as e:
            logger.error(f"Failed to retrieve documents: {e}")
            raise

    def search_similar_documents(
        self,
        query_embedding: List[float],
        k: int = 3,
        similarity_threshold: float = 0.0,
        max_docs_to_search: int = 1000
    ) -> List[Tuple[float, Dict[str, Any]]]:
        """
        Search for similar documents using cosine similarity with optimized retrieval.

        Args:
            query_embedding (List[float]): Query embedding vector
            k (int): Number of top results to return
            similarity_threshold (float): Minimum similarity score threshold
            max_docs_to_search (int): Maximum documents to search through

        Returns:
            List[Tuple[float, Dict[str, Any]]]: List of (similarity_score, document) tuples
        """
        logger.info(f"Searching through up to {max_docs_to_search} documents...")
        documents = self.get_all_documents(max_items=max_docs_to_search)
        scored_documents = []

        processed_count = 0
        for doc in documents:
            if 'embedding' not in doc:
                logger.warning(f"Document {doc.get('id', 'unknown')} missing embedding")
                continue

            similarity = self.cosine_similarity(query_embedding, doc['embedding'])

            if similarity >= similarity_threshold:
                scored_documents.append((similarity, doc))

            processed_count += 1
            if processed_count % 100 == 0:
                logger.info(f"Processed {processed_count} documents...")

        # Sort by similarity score in descending order and return top k
        scored_documents.sort(key=lambda x: x[0], reverse=True)
        top_k_results = scored_documents[:k]

        logger.info(f"Found {len(top_k_results)} similar documents from {processed_count} processed (threshold: {similarity_threshold})")
        return top_k_results

    def generate_response(
        self,
        query: str,
        context_documents: List[Dict[str, Any]],
        max_tokens: int = 150,
        temperature: float = 0.2
    ) -> str:
        """
        Generate a response using GPT model with retrieved context.

        Args:
            query (str): User query
            context_documents (List[Dict[str, Any]]): Retrieved context documents
            max_tokens (int): Maximum tokens in response
            temperature (float): Response creativity (0.0 to 1.0)

        Returns:
            str: Generated response
        """
        # Prepare context from documents
        context_texts = []
        for doc in context_documents:
            if 'data' in doc:
                context_texts.append(json.dumps(doc['data']))
            elif 'text' in doc:
                context_texts.append(doc['text'])

        context = "\n\n".join(context_texts)

        # Create prompt
        prompt = f"""You are a helpful assistant for Mastercard Priceless experiences. Use the context below to answer the user's question.

Context:
{context}

Question: {query}

Answer:"""

        try:
            response = openai.ChatCompletion.create(
                engine=self.gpt_deployment,
                messages=[
                    {
                        "role": "system",
                        "content": "You are a retrieval-augmented generation expert chatbot that finds information from given sources and summarizes responses in 100 words. Do not share any website links and do not exceed more than 150 tokens maximum. If you don't know the answer, ask clarifying questions."
                    },
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                max_tokens=max_tokens,
                temperature=temperature
            )

            return response["choices"][0]["message"]["content"].strip()

        except Exception as e:
            logger.error(f"Failed to generate response: {e}")
            raise

    def search_and_respond(
        self,
        query: str,
        k: int = 3,
        similarity_threshold: float = 0.0
    ) -> Dict[str, Any]:
        """
        Complete RAG pipeline: search for relevant documents and generate response.

        Args:
            query (str): User query
            k (int): Number of documents to retrieve
            similarity_threshold (float): Minimum similarity threshold

        Returns:
            Dict[str, Any]: Response with answer and metadata
        """
        logger.info(f"Processing query: {query}")

        # Generate query embedding
        query_embedding = self.get_embedding(query)

        # Search for similar documents
        similar_docs = self.search_similar_documents(
            query_embedding, k=k, similarity_threshold=similarity_threshold
        )

        if not similar_docs:
            return {
                "answer": "I couldn't find any relevant information for your query. Could you please rephrase or provide more details?",
                "sources_count": 0,
                "similarity_scores": []
            }

        # Extract documents and similarity scores
        documents = [doc for _, doc in similar_docs]
        similarity_scores = [score for score, _ in similar_docs]

        # Generate response
        answer = self.generate_response(query, documents)

        return {
            "answer": answer,
            "sources_count": len(documents),
            "similarity_scores": similarity_scores,
            "top_similarity": max(similarity_scores) if similarity_scores else 0.0
        }


class ChatInterface:
    """Interactive chat interface for the RAG system."""

    def __init__(self):
        """Initialize the chat interface."""
        self.search_retrieval = SearchRetrieval()
        logger.info("Chat interface initialized")

    def start_chat(self):
        """Start an interactive chat session."""
        print("🎯 Mastercard Priceless Assistant")
        print("Ask me about exclusive experiences and offers!")
        print("Type 'quit' or 'exit' to end the conversation.\n")

        while True:
            try:
                # Get user input
                query = input("You: ").strip()

                if query.lower() in ['quit', 'exit', 'bye']:
                    print("Thank you for using Mastercard Priceless Assistant! 👋")
                    break

                if not query:
                    print("Please enter a question.")
                    continue

                # Process query
                print("🔍 Searching for relevant information...")
                result = self.search_retrieval.search_and_respond(query, k=3)

                # Display response
                print(f"\n🤖 Assistant: {result['answer']}")
                print(f"📊 Sources found: {result['sources_count']}")
                if result['similarity_scores']:
                    print(f"🎯 Relevance score: {result['top_similarity']:.3f}")
                print("-" * 50)

            except KeyboardInterrupt:
                print("\n\nGoodbye! 👋")
                break
            except Exception as e:
                logger.error(f"Error processing query: {e}")
                print("❌ Sorry, I encountered an error. Please try again.")


def main():
    """Main function to run the search and retrieval system."""
    import argparse

    parser = argparse.ArgumentParser(description="Mastercard Priceless Search & Retrieval")
    parser.add_argument(
        "--mode",
        choices=["chat", "single"],
        default="chat",
        help="Run in interactive chat mode or single query mode"
    )
    parser.add_argument(
        "--query",
        type=str,
        help="Single query to process (required for single mode)"
    )
    parser.add_argument(
        "--top-k",
        type=int,
        default=3,
        help="Number of top documents to retrieve"
    )
    parser.add_argument(
        "--threshold",
        type=float,
        default=0.0,
        help="Similarity threshold for document retrieval"
    )

    args = parser.parse_args()

    try:
        if args.mode == "chat":
            # Start interactive chat
            chat = ChatInterface()
            chat.start_chat()

        elif args.mode == "single":
            if not args.query:
                print("Error: --query is required for single mode")
                return

            # Process single query
            search_retrieval = SearchRetrieval()
            result = search_retrieval.search_and_respond(
                args.query,
                k=args.top_k,
                similarity_threshold=args.threshold
            )

            print(f"Query: {args.query}")
            print(f"Answer: {result['answer']}")
            print(f"Sources: {result['sources_count']}")
            print(f"Top similarity: {result['top_similarity']:.3f}")

    except Exception as e:
        logger.error(f"Application failed: {e}")
        raise


if __name__ == "__main__":
    main()
