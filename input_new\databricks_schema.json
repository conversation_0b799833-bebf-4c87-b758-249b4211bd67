{"table_name": "experience_embeddings", "schema": {"id": "STRING", "content": "STRING", "keywords": "ARRAY<STRING>", "metadata": "STRUCT<original_id:STRING,type:STRING,primary_category:STRING,location_city:STRING,location_country:STRING,pricing_amount:DOUBLE,pricing_currency:STRING,pricing_for_people:INT,valid_until:STRING,provider_name:STRING,media_url:STRING,coordinates:STRUCT<latitude:DOUBLE,longitude:DOUBLE>>", "title": "STRING", "description": "STRING", "category": "STRING", "location": "STRING", "price": "DOUBLE", "currency": "STRING", "created_at": "TIMESTAMP"}, "vector_search_config": {"primary_key": "id", "embedding_source_column": "content", "embedding_model": "databricks-bge-large-en", "sync_mode": "TRIGGERED"}, "hybrid_search_fields": ["keywords", "title", "category", "location"]}