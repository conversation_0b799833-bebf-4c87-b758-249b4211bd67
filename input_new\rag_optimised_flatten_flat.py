"""
Simple RAG Flatten to JSONL Script
Converts nested JSON to flat JSONL format
"""

import json

def flatten_json(nested_json, prefix="", separator="_"):
    """Flatten nested JSON into flat dictionary with concatenated keys."""
    flattened = {}
    
    for key, value in nested_json.items():
        new_key = f"{prefix}{separator}{key}" if prefix else key
        
        if isinstance(value, dict):
            flattened.update(flatten_json(value, new_key, separator))
        elif isinstance(value, list) and len(value) > 0 and isinstance(value[0], dict):
            for i, item in enumerate(value):
                flattened.update(flatten_json(item, f"{new_key}{separator}{i}", separator))
        elif isinstance(value, list) and len(value) > 0:
            flattened[new_key] = ", ".join(str(item) for item in value)
        else:
            flattened[new_key] = value
    
    return flattened

def convert_to_flat_jsonl(input_file, output_file):
    """Convert nested JSON to flat JSONL format"""
    
    # Load input file
    with open(input_file, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    # Convert to flat JSONL
    with open(output_file, 'w', encoding='utf-8') as f:
        for product in data.get("products", []):
            flattened_product = flatten_json(product)
            f.write(json.dumps(flattened_product, ensure_ascii=False) + '\n')
    
    print(f"Converted {len(data.get('products', []))} products to flat JSONL format")

if __name__ == "__main__":
    convert_to_flat_jsonl(
        'input_new/rag_optimized_product.json',
        'input_new/rag_optimized_flattened_flat.jsonl'
    )
