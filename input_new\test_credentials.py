"""
Credential Testing Script for Mastercard Priceless RAG System

This script tests the connectivity and authentication for:
1. Azure OpenAI API (embedding and GPT models)
2. Azure Cosmos DB

Run this script to verify your .env configuration before running the main modules.
"""

import os
import logging
from dotenv import load_dotenv
import openai
from azure.cosmos import CosmosClient
from azure.cosmos.exceptions import CosmosResourceExistsError

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def test_environment_variables():
    """Test if all required environment variables are set."""
    logger.info("🔍 Testing environment variables...")

    required_vars = [
        'OPENAI_API_KEY',
        'AZURE_EXISTING_AIPROJECT_ENDPOINT',
        'COSMOS_ENDPOINT',
        'COSMOS_KEY'
    ]

    missing_vars = []
    for var in required_vars:
        value = os.getenv(var)
        if not value:
            missing_vars.append(var)
        else:
            # Show partial value for security
            masked_value = value[:8] + "..." + value[-4:] if len(value) > 12 else "***"
            logger.info(f"✅ {var}: {masked_value}")

    if missing_vars:
        logger.error(f"❌ Missing environment variables: {missing_vars}")
        return False

    logger.info("✅ All required environment variables are set")
    return True


def test_openai_connection():
    """Test Azure OpenAI API connection and embedding generation."""
    logger.info("🔍 Testing Azure OpenAI connection...")

    try:
        # Configure OpenAI
        openai.api_type = "azure"
        openai.api_base = os.getenv('AZURE_EXISTING_AIPROJECT_ENDPOINT')
        openai.api_key = os.getenv('OPENAI_API_KEY')
        openai.api_version = "2023-05-15"

        # Test embedding generation
        logger.info("Testing embedding generation...")
        response = openai.Embedding.create(
            input="Test embedding for Mastercard Priceless",
            engine="ada-embedding"
        )

        embedding = response['data'][0]['embedding']
        logger.info(f"✅ Embedding generated successfully (dimension: {len(embedding)})")

        # Test GPT model (optional - comment out if model not available)
        try:
            logger.info("Testing GPT model...")
            gpt_response = openai.ChatCompletion.create(
                engine="gpt-4o-mini",
                messages=[
                    {"role": "system", "content": "You are a helpful assistant."},
                    {"role": "user", "content": "Say hello in one word."}
                ],
                max_tokens=10
            )

            answer = gpt_response["choices"][0]["message"]["content"]
            logger.info(f"✅ GPT model working: {answer.strip()}")

        except Exception as e:
            logger.warning(f"⚠️ GPT model test failed (this might be expected): {e}")

        return True

    except Exception as e:
        logger.error(f"❌ Azure OpenAI connection failed: {e}")
        return False


def test_cosmos_db_connection():
    """Test Azure Cosmos DB connection and basic operations."""
    logger.info("🔍 Testing Azure Cosmos DB connection...")

    try:
        # Initialize Cosmos client
        cosmos_endpoint = os.getenv('COSMOS_ENDPOINT')
        cosmos_key = os.getenv('COSMOS_KEY')
        database_name = os.getenv('DATABASE_NAME', 'cosmicworks')
        container_name = os.getenv('CONTAINER_NAME', 'products')

        cosmos_client = CosmosClient(cosmos_endpoint, cosmos_key)
        logger.info("✅ Cosmos DB client initialized")

        # Test database access/creation
        try:
            database = cosmos_client.create_database(database_name)
            logger.info(f"✅ Created database: {database_name}")
        except CosmosResourceExistsError:
            database = cosmos_client.get_database_client(database_name)
            logger.info(f"✅ Connected to existing database: {database_name}")

        # Test container access/creation
        try:
            from azure.cosmos import PartitionKey
            # Try creating container without throughput first (for serverless accounts)
            container = database.create_container(
                id=container_name,
                partition_key=PartitionKey(path="/id")
            )
            logger.info(f"✅ Created container: {container_name} (serverless)")
        except CosmosResourceExistsError:
            container = database.get_container_client(container_name)
            logger.info(f"✅ Connected to existing container: {container_name}")
        except Exception as e:
            if "serverless" in str(e).lower():
                logger.info("✅ Serverless account detected")
                try:
                    container = database.get_container_client(container_name)
                    logger.info(f"✅ Connected to existing container: {container_name}")
                except:
                    logger.error(f"❌ Container {container_name} doesn't exist and can't be created")
                    return False
            else:
                # Try with throughput for provisioned accounts
                try:
                    container = database.create_container(
                        id=container_name,
                        partition_key=PartitionKey(path="/id"),
                        offer_throughput=400
                    )
                    logger.info(f"✅ Created container: {container_name} (provisioned)")
                except CosmosResourceExistsError:
                    container = database.get_container_client(container_name)
                    logger.info(f"✅ Connected to existing container: {container_name}")
                except Exception as inner_e:
                    logger.error(f"❌ Failed to create/access container: {inner_e}")
                    return False

        # Test basic operations
        test_doc = {
            "id": "test_credential_check",
            "test": True,
            "message": "Credential test document"
        }

        # Try to create/update test document
        container.upsert_item(test_doc)
        logger.info("✅ Test document created/updated successfully")

        # Try to read test document
        retrieved_doc = container.read_item(
            item="test_credential_check",
            partition_key="test_credential_check"
        )
        logger.info("✅ Test document retrieved successfully")

        # Clean up test document
        container.delete_item(
            item="test_credential_check",
            partition_key="test_credential_check"
        )
        logger.info("✅ Test document cleaned up")

        return True

    except Exception as e:
        logger.error(f"❌ Cosmos DB connection failed: {e}")
        return False


def main():
    """Run all credential tests."""
    print("🧪 Mastercard Priceless Credential Test Suite")
    print("=" * 50)

    tests = [
        ("Environment Variables", test_environment_variables),
        ("Azure OpenAI", test_openai_connection),
        ("Azure Cosmos DB", test_cosmos_db_connection)
    ]

    results = {}

    for test_name, test_func in tests:
        print(f"\n📋 Running {test_name} test...")
        try:
            results[test_name] = test_func()
        except Exception as e:
            logger.error(f"❌ {test_name} test failed with exception: {e}")
            results[test_name] = False

    # Summary
    print("\n" + "=" * 50)
    print("📊 Test Results Summary:")
    print("=" * 50)

    all_passed = True
    for test_name, passed in results.items():
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"{test_name}: {status}")
        if not passed:
            all_passed = False

    print("=" * 50)
    if all_passed:
        print("🎉 All tests passed! Your credentials are working correctly.")
        print("You can now run the main modules:")
        print("  - python data_processor.py")
        print("  - python search_retrieval.py")
    else:
        print("⚠️ Some tests failed. Please check your .env configuration.")
        print("Make sure all required credentials are correct and services are accessible.")


if __name__ == "__main__":
    main()
