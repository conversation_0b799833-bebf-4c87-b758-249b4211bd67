import json
import uuid
from typing import Dict, List, Any

def flatten_json(nested_json: Dict, prefix: str = "", separator: str = "_") -> Dict:
    """
    Flatten a nested JSON object into a flat dictionary with concatenated keys.

    Args:
        nested_json: The nested JSON object to flatten
        prefix: Prefix for the flattened keys
        separator: Separator to use between key parts

    Returns:
        A flattened dictionary
    """
    flattened = {}

    for key, value in nested_json.items():
        new_key = f"{prefix}{separator}{key}" if prefix else key

        if isinstance(value, dict):
            # Recursively flatten nested dictionaries
            flattened.update(flatten_json(value, new_key, separator))
        elif isinstance(value, list) and len(value) > 0 and isinstance(value[0], dict):
            # Handle lists of dictionaries
            for i, item in enumerate(value):
                flattened.update(flatten_json(item, f"{new_key}{separator}{i}", separator))
        elif isinstance(value, list) and len(value) > 0 and isinstance(value[0], (str, int, float, bool)):
            # Join simple lists into strings
            flattened[new_key] = ", ".join(str(item) for item in value)
        else:
            # Add simple values directly
            flattened[new_key] = value

    return flattened

def create_text_representation(flattened_dict: Dict) -> str:
    """
    Create a text representation of a flattened dictionary.

    Args:
        flattened_dict: A flattened dictionary

    Returns:
        A text representation of the dictionary
    """
    text_parts = []

    for key, value in flattened_dict.items():
        if value is not None and value != "":
            # Format the key for readability
            formatted_key = key.replace("_", " ").title()
            text_parts.append(f"{formatted_key}: {value}")

    return "\n".join(text_parts)

def process_rag_data(input_file: str, output_file: str):
    """
    Process RAG data by flattening and chunking using product-level chunking.

    Args:
        input_file: Path to the input JSON file
        output_file: Path to the output JSON file
    """
    # Load the input JSON data
    with open(input_file, 'r', encoding='utf-8') as f:
        data = json.load(f)

    products = data.get("products", [])
    chunks = []

    # Create product-level chunks
    for product in products:
        # Flatten the product
        flattened_product = flatten_json(product)

        # Create a product-level chunk
        chunk = {
            "chunk_id": str(uuid.uuid4()),
            "product_id": flattened_product.get("id", ""),
            "chunk_type": "product",
            "content": flattened_product,
            "text": create_text_representation(flattened_product),
            "metadata": {
                "title": flattened_product.get("title", ""),
                "type": flattened_product.get("type", ""),
                "primary_category": flattened_product.get("primary_category", ""),
                "valid_until": flattened_product.get("valid_until", ""),
                "location_city": flattened_product.get("location_city", ""),
                "location_country": flattened_product.get("location_country", "")
            }
        }

        chunks.append(chunk)

    # Create the output data
    output_data = {
        "chunks": chunks,
        "metadata": {
            "total_chunks": len(chunks),
            "chunking_strategy": "product",
            "source": data.get("metadata", {}).get("source", ""),
            "generated_at": data.get("metadata", {}).get("generated_at", "")
        }
    }

    # Save the output JSON data
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(output_data, f, indent=2, ensure_ascii=False)

    print(f"Successfully processed {len(products)} products into {len(chunks)} chunks using product-level chunking.")
    print(f"Output saved to {output_file}")

def main():
    """Main function to run the script."""
    import argparse

    parser = argparse.ArgumentParser(description="Process RAG data by flattening and chunking")
    parser.add_argument("--input", type=str, default="rag_optimized_product.json", help="Input file path")
    parser.add_argument("--output", type=str, default="rag_chunked_product.json", help="Output file path")

    args = parser.parse_args()

    process_rag_data(args.input, args.output)

if __name__ == "__main__":
    main()

