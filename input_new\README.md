# Mastercard Priceless RAG System

A modular, production-ready Retrieval-Augmented Generation (RAG) system for Mastercard Priceless experiences, built with Azure OpenAI and Cosmos DB. Optimized for both serverless and provisioned Cosmos DB accounts.

## 🏗️ Architecture

The system is split into modular components for scalability and maintainability:

1. **`data_processor.py`** - Data processing and embedding storage
2. **`search_retrieval.py`** - Optimized vector search and response generation
3. **`test_credentials.py`** - Credential verification and connectivity testing
4. **`check_database_size.py`** - Database analytics and performance insights
5. **`setup_cosmos_container.py`** - Automated Cosmos DB setup for serverless accounts
6. **`install_dependencies.py`** - Intelligent dependency installation with error handling

## 📋 Prerequisites

- Python 3.8+
- Azure OpenAI account with embedding and GPT models
- Azure Cosmos DB account (serverless or provisioned)
- Required API keys and endpoints

## 🚀 Quick Start

### 1. Install Dependencies

```bash
# Automated installation with error handling
python install_dependencies.py

# Or manual installation
pip install -r requirements.txt
```

### 2. Configure Environment

Update the `.env` file with your credentials:

```env
# Azure OpenAI Configuration
OPENAI_API_KEY="your-openai-api-key"
AZURE_EXISTING_AIPROJECT_ENDPOINT="https://your-endpoint.openai.azure.com/"

# Azure Cosmos DB Configuration
COSMOS_ENDPOINT="https://your-cosmos-account.documents.azure.com:443/"
COSMOS_KEY="your-cosmos-key"
DATABASE_NAME="cosmicworks"
CONTAINER_NAME="products"
```

### 3. Verify Setup

```bash
# Test all credentials and connections
python test_credentials.py

# Check database size and performance characteristics
python check_database_size.py

# Setup Cosmos container (for serverless accounts)
python setup_cosmos_container.py
```

### 4. Process and Store Data

```bash
# Process your JSON data and store embeddings
python data_processor.py
```

Make sure to update the `json_file_path` in the `main()` function to point to your data file.

### 5. Start the Search Interface

```bash
# Interactive chat mode
python search_retrieval.py --mode chat

# Single query mode with performance optimization
python search_retrieval.py --mode single --query "Show me dining offers in New York" --top-k 5

# Advanced search with custom parameters
python search_retrieval.py --mode single --query "luxury experiences" --top-k 10 --threshold 0.7
```

## 📁 File Structure

```
input_new/
├── data_processor.py           # Data processing and embedding storage
├── search_retrieval.py         # Optimized search and retrieval functionality
├── test_credentials.py         # Credential verification and testing
├── check_database_size.py      # Database analytics and size checking
├── setup_cosmos_container.py   # Cosmos DB container setup for serverless
├── install_dependencies.py     # Intelligent dependency installer
├── requirements.txt            # Python dependencies
├── .env                       # Environment variables (keep secure!)
└── README.md                  # This file
```

## 🔧 Module Details

### DataProcessor Class

**Purpose**: Handles data processing pipeline from raw JSON to stored embeddings.

**Key Methods**:
- `load_json_data()` - Load JSON data from file
- `flatten_data()` - Flatten nested JSON structures
- `chunk_data()` - Split large documents into chunks
- `generate_embeddings()` - Create embeddings using Azure OpenAI
- `store_embeddings()` - Save to Cosmos DB
- `process_and_store_data()` - Complete pipeline

### SearchRetrieval Class

**Purpose**: Handles optimized search, retrieval, and response generation.

**Key Methods**:
- `get_embedding()` - Generate query embeddings
- `get_all_documents()` - Efficient document retrieval with pagination limits
- `search_similar_documents()` - Optimized vector similarity search with performance monitoring
- `generate_response()` - GPT-powered response generation
- `search_and_respond()` - Complete RAG pipeline with performance optimization

**Performance Features**:
- Document limit controls to prevent memory issues
- Progress logging for large datasets
- Configurable similarity thresholds
- Batch processing optimization

## 🎯 Usage Examples

### Processing Data

```python
from data_processor import DataProcessor

processor = DataProcessor()
processor.process_and_store_data("your_data.json")
```

### Searching and Retrieving

```python
from search_retrieval import SearchRetrieval

search = SearchRetrieval()

# Basic search
result = search.search_and_respond(
    "Show me culinary experiences in Europe",
    k=5,
    similarity_threshold=0.7
)
print(result['answer'])

# Optimized search for large datasets
result = search.search_similar_documents(
    query_embedding,
    k=10,
    similarity_threshold=0.6,
    max_docs_to_search=2000  # Limit for performance
)
```

### Interactive Chat

```python
from search_retrieval import ChatInterface

chat = ChatInterface()
chat.start_chat()
```

## ⚙️ Configuration Options

### Search Parameters

- `k`: Number of top documents to retrieve (default: 3, recommended: 5-10)
- `similarity_threshold`: Minimum cosine similarity score (default: 0.0, recommended: 0.6-0.8)
- `max_docs_to_search`: Maximum documents to search through (default: 1000, adjust based on dataset size)
- `max_tokens`: Maximum response length (default: 150)
- `temperature`: Response creativity 0.0-1.0 (default: 0.2)

### Performance Tuning

- **Small datasets (<1,000 docs)**: Use default settings
- **Medium datasets (1,000-10,000 docs)**: Set `max_docs_to_search=2000`, `similarity_threshold=0.6`
- **Large datasets (>10,000 docs)**: Set `max_docs_to_search=1000`, `similarity_threshold=0.7`, consider vector indexing

### Environment Variables

| Variable | Description | Required |
|----------|-------------|----------|
| `OPENAI_API_KEY` | Azure OpenAI API key | Yes |
| `AZURE_EXISTING_AIPROJECT_ENDPOINT` | Azure OpenAI endpoint | Yes |
| `COSMOS_ENDPOINT` | Cosmos DB endpoint | Yes |
| `COSMOS_KEY` | Cosmos DB access key | Yes |
| `DATABASE_NAME` | Cosmos DB database name | No (default: cosmicworks) |
| `CONTAINER_NAME` | Cosmos DB container name | No (default: products) |

## 🔒 Security Best Practices

1. **Never commit `.env` files** to version control
2. **Rotate API keys** regularly
3. **Use Azure Key Vault** for production deployments
4. **Implement proper access controls** on Cosmos DB
5. **Monitor API usage** and costs

## 🚨 Troubleshooting

### Common Issues

1. **Missing environment variables**
   ```
   ValueError: Missing required environment variables: ['OPENAI_API_KEY']
   ```
   **Solution**: Run `python test_credentials.py` to verify all variables are set correctly.

2. **Cosmos DB serverless account errors**
   ```
   Setting offer throughput or autopilot on container is not supported for serverless accounts
   ```
   **Solution**: Run `python setup_cosmos_container.py` - the system now auto-detects serverless accounts.

3. **Search hanging or taking too long**
   ```
   System appears to hang during search operations
   ```
   **Solution**:
   - Press `Ctrl+C` to stop
   - Run `python check_database_size.py` to check dataset size
   - Use optimized search with document limits

4. **OpenAI API errors**
   ```
   Failed to generate embedding
   ```
   **Solution**: Check your API key and ensure you have access to the embedding model.

5. **Memory issues with large datasets**
   ```
   Out of memory or very slow performance
   ```
   **Solution**: Reduce `max_docs_to_search` parameter or implement batch processing.

## 📊 Performance Tips

1. **Document limits**: Use `max_docs_to_search` parameter to control memory usage and response time
2. **Similarity threshold**: Use thresholds 0.6-0.8 to filter irrelevant results and improve speed
3. **Batch processing**: Process embeddings in batches for better performance
4. **Database monitoring**: Use `check_database_size.py` to understand your dataset characteristics
5. **Cosmos DB optimization**:
   - Use serverless for development and small workloads
   - Consider provisioned throughput for production with consistent load
6. **Caching**: Consider caching frequent queries and embeddings
7. **Vector indexing**: For production with >10,000 documents, consider implementing vector indexing

### Performance Benchmarks

| Dataset Size | Recommended Settings | Expected Response Time |
|-------------|---------------------|----------------------|
| <1,000 docs | Default settings | <2 seconds |
| 1,000-5,000 docs | `max_docs_to_search=2000` | 2-5 seconds |
| 5,000-10,000 docs | `max_docs_to_search=1000`, `threshold=0.7` | 3-8 seconds |
| >10,000 docs | `max_docs_to_search=500`, `threshold=0.8` | 2-5 seconds |

## 🔄 Deployment

### Development Environment
```bash
# Quick setup for development
python install_dependencies.py
python test_credentials.py
python setup_cosmos_container.py
python data_processor.py
```

### Production Deployment

1. **Container Deployment**: Use Azure Container Instances or App Service
2. **Security**: Store secrets in Azure Key Vault, never commit `.env` files
3. **Monitoring**: Implement proper logging and monitoring with Application Insights
4. **CI/CD**: Set up automated pipelines with GitHub Actions or Azure DevOps
5. **Scaling**: Configure auto-scaling based on usage patterns
6. **Performance**: Implement vector indexing for large datasets
7. **Backup**: Regular backup of Cosmos DB data

### Environment-Specific Configurations

| Environment | Cosmos DB | Document Limit | Similarity Threshold |
|------------|-----------|----------------|---------------------|
| Development | Serverless | 500 | 0.6 |
| Staging | Serverless/Provisioned | 1000 | 0.7 |
| Production | Provisioned | 2000+ | 0.8 |

## 🆕 Recent Updates

- ✅ **Serverless Cosmos DB Support**: Auto-detection and setup for serverless accounts
- ✅ **Performance Optimization**: Document limits and progress monitoring
- ✅ **Enhanced Error Handling**: Better error messages and recovery
- ✅ **Database Analytics**: Size checking and performance insights
- ✅ **Automated Setup**: Intelligent dependency installation and container setup
- ✅ **Production Ready**: Comprehensive logging, monitoring, and deployment guides

## 📝 License

This project is proprietary to Mastercard. All rights reserved.

---

**Need Help?**
- Run `python test_credentials.py` for connectivity issues
- Run `python check_database_size.py` for performance analysis
- Check the troubleshooting section above for common solutions
