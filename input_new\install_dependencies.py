"""
Installation Help<PERSON> for Mastercard Priceless RAG System

This script helps install dependencies and troubleshoot common installation issues.
"""

import subprocess
import sys
import os
import platform

def run_command(command, description):
    """Run a command and handle errors."""
    print(f"\n🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ {description} completed successfully")
            if result.stdout.strip():
                print(f"Output: {result.stdout.strip()}")
            return True
        else:
            print(f"❌ {description} failed")
            print(f"Error: {result.stderr.strip()}")
            return False
    except Exception as e:
        print(f"❌ {description} failed with exception: {e}")
        return False

def check_python_version():
    """Check if Python version is compatible."""
    print("🔍 Checking Python version...")
    version = sys.version_info
    print(f"Python version: {version.major}.{version.minor}.{version.micro}")
    
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("❌ Python 3.8+ is required")
        return False
    
    print("✅ Python version is compatible")
    return True

def upgrade_pip():
    """Upgrade pip to latest version."""
    return run_command(
        f"{sys.executable} -m pip install --upgrade pip",
        "Upgrading pip"
    )

def install_requirements():
    """Install requirements with error handling."""
    requirements_file = "requirements.txt"
    
    if not os.path.exists(requirements_file):
        print(f"❌ {requirements_file} not found")
        return False
    
    # Try different installation strategies
    strategies = [
        f"{sys.executable} -m pip install -r {requirements_file}",
        f"{sys.executable} -m pip install -r {requirements_file} --no-cache-dir",
        f"{sys.executable} -m pip install -r {requirements_file} --force-reinstall",
    ]
    
    for i, strategy in enumerate(strategies, 1):
        print(f"\n📦 Installation strategy {i}/{len(strategies)}")
        if run_command(strategy, f"Installing requirements (strategy {i})"):
            return True
        print(f"Strategy {i} failed, trying next...")
    
    return False

def install_individual_packages():
    """Install packages individually if bulk install fails."""
    print("\n🔧 Trying individual package installation...")
    
    core_packages = [
        "openai==0.28.0",
        "azure-cosmos>=4.9.0", 
        "python-dotenv>=1.0.0",
        "numpy>=1.21.0"
    ]
    
    success_count = 0
    for package in core_packages:
        if run_command(f"{sys.executable} -m pip install {package}", f"Installing {package}"):
            success_count += 1
    
    print(f"\n📊 Successfully installed {success_count}/{len(core_packages)} core packages")
    return success_count == len(core_packages)

def test_imports():
    """Test if key packages can be imported."""
    print("\n🧪 Testing package imports...")
    
    test_packages = [
        ("openai", "OpenAI"),
        ("azure.cosmos", "Azure Cosmos DB"),
        ("dotenv", "python-dotenv"),
        ("numpy", "NumPy")
    ]
    
    success_count = 0
    for package, name in test_packages:
        try:
            __import__(package)
            print(f"✅ {name} import successful")
            success_count += 1
        except ImportError as e:
            print(f"❌ {name} import failed: {e}")
    
    print(f"\n📊 {success_count}/{len(test_packages)} packages imported successfully")
    return success_count == len(test_packages)

def show_system_info():
    """Display system information for troubleshooting."""
    print("\n💻 System Information:")
    print(f"Platform: {platform.platform()}")
    print(f"Python: {sys.version}")
    print(f"Architecture: {platform.architecture()}")
    
    # Check if we're in a virtual environment
    if hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
        print("✅ Running in virtual environment")
    else:
        print("⚠️ Not running in virtual environment (recommended to use venv)")

def main():
    """Main installation process."""
    print("🚀 Mastercard Priceless RAG System - Dependency Installer")
    print("=" * 60)
    
    # Show system info
    show_system_info()
    
    # Check Python version
    if not check_python_version():
        print("\n❌ Installation aborted due to incompatible Python version")
        return
    
    # Upgrade pip
    upgrade_pip()
    
    # Try to install requirements
    if install_requirements():
        print("\n🎉 All dependencies installed successfully!")
    else:
        print("\n⚠️ Bulk installation failed, trying individual packages...")
        if install_individual_packages():
            print("\n🎉 Core dependencies installed successfully!")
        else:
            print("\n❌ Installation failed. Please check the errors above.")
            return
    
    # Test imports
    if test_imports():
        print("\n🎉 All packages are working correctly!")
        print("\nNext steps:")
        print("1. Configure your .env file with credentials")
        print("2. Run: python test_credentials.py")
        print("3. Run: python data_processor.py")
        print("4. Run: python search_retrieval.py")
    else:
        print("\n⚠️ Some packages failed to import. Please check the errors above.")

if __name__ == "__main__":
    main()
