
-- Create Delta table for embeddings
CREATE TABLE IF NOT EXISTS experience_embeddings (
    id STRING,
    content STRING,
    keywords ARRAY<STRING>,
    metadata STRUCT<
        original_id: STRING,
        type: STRING,
        primary_category: STRING,
        location_city: STRING,
        location_country: STRING,
        pricing_amount: DOUBLE,
        pricing_currency: STRING,
        pricing_for_people: INT,
        valid_until: STRING,
        provider_name: STRING,
        media_url: STRING,
        coordinates: STRUCT<latitude: DOUBLE, longitude: DOUBLE>
    >,
    title STRING,
    description STRING,
    category STRING,
    location STRING,
    price DOUBLE,
    currency STRING,
    created_at TIMESTAMP
) USING DELTA;

-- Create vector search index for hybrid retrieval
CREATE VECTOR SEARCH INDEX IF NOT EXISTS experience_vector_index
ON experience_embeddings (content)
USING ENDPOINT 'your_vector_search_endpoint'
WITH (
    'embedding_model' = 'databricks-bge-large-en',
    'sync_mode' = 'TRIGGERED'
);

-- Example hybrid search query
-- This combines vector similarity with keyword filtering
SELECT id, title, location, category, price
FROM VECTOR_SEARCH(
    index => 'experience_vector_index',
    query => 'underwater dining experience',
    num_results => 10,
    filters => map(
        'category', 'Culinary',
        'location_country', 'Thailand'
    )
);
