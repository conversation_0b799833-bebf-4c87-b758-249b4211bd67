"""
Databricks Mosaic Vector Search Embedding Script

This script processes flattened JSONL data and creates embeddings optimized for 
Databricks Mosaic Vector Search with hybrid retrieval capabilities.

Features:
- Optimized for hybrid search (keywords + cosine similarity)
- Modular and sequential processing
- Proper metadata handling for Mosaic Vector Search
"""

import json
import pandas as pd
from typing import List, Dict, Any
import hashlib
from datetime import datetime

def load_jsonl_data(file_path: str) -> List[Dict]:
    """Load data from JSONL file."""
    data = []
    with open(file_path, 'r', encoding='utf-8') as f:
        for line in f:
            if line.strip():
                data.append(json.loads(line))
    print(f"Loaded {len(data)} records from {file_path}")
    return data

def create_searchable_content(record: Dict) -> str:
    """Create optimized searchable content for embedding."""
    # Priority fields for semantic search
    content_parts = []
    
    # High priority: Title and descriptions
    if record.get('title'):
        content_parts.append(f"Title: {record['title']}")
    
    if record.get('description_short'):
        content_parts.append(f"Description: {record['description_short']}")
    
    if record.get('description_highlights'):
        content_parts.append(f"Highlights: {record['description_highlights']}")
    
    # Medium priority: Location and category
    location_parts = []
    if record.get('location_city'):
        location_parts.append(record['location_city'])
    if record.get('location_country'):
        location_parts.append(record['location_country'])
    if record.get('location_venue'):
        location_parts.append(record['location_venue'])
    
    if location_parts:
        content_parts.append(f"Location: {', '.join(location_parts)}")
    
    if record.get('primary_category'):
        content_parts.append(f"Category: {record['primary_category']}")
    
    if record.get('provider_name'):
        content_parts.append(f"Provider: {record['provider_name']}")
    
    # Lower priority: Additional context
    if record.get('provider_description'):
        content_parts.append(f"About: {record['provider_description']}")
    
    return ' | '.join(content_parts)

def extract_keywords_for_hybrid_search(record: Dict) -> List[str]:
    """Extract and normalize keywords for hybrid search."""
    keywords = []
    
    # Extract from existing keywords field
    if record.get('keywords'):
        keywords.extend([k.strip().lower() for k in record['keywords'].split(',')])
    
    # Extract from categories
    if record.get('categories'):
        categories = [c.strip().lower() for c in record['categories'].split(',')]
        keywords.extend(categories)
    
    # Extract from location fields
    location_keywords = []
    for field in ['location_city', 'location_country', 'location_venue', 'location_region']:
        if record.get(field):
            location_keywords.append(record[field].lower())
    keywords.extend(location_keywords)
    
    # Extract from primary category
    if record.get('primary_category'):
        keywords.append(record['primary_category'].lower())
    
    # Extract from provider name
    if record.get('provider_name'):
        keywords.append(record['provider_name'].lower())
    
    # Remove duplicates and empty strings
    keywords = list(set([k for k in keywords if k and len(k) > 1]))
    
    return keywords

def create_mosaic_record(record: Dict, index: int) -> Dict:
    """Create a record optimized for Databricks Mosaic Vector Search."""
    
    # Generate unique ID for Mosaic Vector Search
    record_id = record.get('id', f"doc_{index}")
    
    # Create searchable content for embedding
    content_for_embedding = create_searchable_content(record)
    
    # Extract keywords for hybrid search
    keywords = extract_keywords_for_hybrid_search(record)
    
    # Create metadata for filtering and display
    metadata = {
        'original_id': record.get('id', ''),
        'type': record.get('type', ''),
        'primary_category': record.get('primary_category', ''),
        'location_city': record.get('location_city', ''),
        'location_country': record.get('location_country', ''),
        'pricing_amount': record.get('pricing_amount', 0),
        'pricing_currency': record.get('pricing_currency', ''),
        'pricing_for_people': record.get('pricing_for_people', 1),
        'valid_until': record.get('valid_until', ''),
        'provider_name': record.get('provider_name', ''),
        'media_url': record.get('media_url', ''),
        'coordinates': {
            'latitude': record.get('location_coordinates_latitude', 0),
            'longitude': record.get('location_coordinates_longitude', 0)
        }
    }
    
    # Create the Mosaic Vector Search optimized record
    mosaic_record = {
        'id': record_id,
        'content': content_for_embedding,
        'keywords': keywords,
        'metadata': metadata,
        'title': record.get('title', ''),
        'description': record.get('description_short', ''),
        'category': record.get('primary_category', ''),
        'location': f"{record.get('location_city', '')}, {record.get('location_country', '')}".strip(', '),
        'price': record.get('pricing_amount', 0),
        'currency': record.get('pricing_currency', ''),
        'created_at': datetime.now().isoformat()
    }
    
    return mosaic_record

def process_for_mosaic_vector_search(input_file: str, output_file: str):
    """Process JSONL data for Databricks Mosaic Vector Search."""
    
    print("🚀 Starting Mosaic Vector Search data processing...")
    
    # Step 1: Load data
    print("📥 Loading JSONL data...")
    raw_data = load_jsonl_data(input_file)
    
    # Step 2: Process each record
    print("🔄 Processing records for Mosaic Vector Search...")
    mosaic_records = []
    
    for i, record in enumerate(raw_data):
        mosaic_record = create_mosaic_record(record, i)
        mosaic_records.append(mosaic_record)
        
        if (i + 1) % 10 == 0:
            print(f"   Processed {i + 1}/{len(raw_data)} records")
    
    # Step 3: Save processed data
    print("💾 Saving processed data...")
    with open(output_file, 'w', encoding='utf-8') as f:
        for record in mosaic_records:
            f.write(json.dumps(record, ensure_ascii=False) + '\n')
    
    print(f"✅ Successfully processed {len(mosaic_records)} records")
    print(f"📁 Output saved to: {output_file}")
    
    # Step 4: Display sample record
    print("\n📋 Sample processed record:")
    sample = mosaic_records[0]
    print(f"ID: {sample['id']}")
    print(f"Title: {sample['title'][:100]}...")
    print(f"Content: {sample['content'][:200]}...")
    print(f"Keywords: {sample['keywords'][:5]}...")
    print(f"Location: {sample['location']}")
    print(f"Category: {sample['category']}")
    
    return mosaic_records

def create_databricks_schema_info():
    """Generate schema information for Databricks table creation."""
    
    schema_info = {
        "table_name": "experience_embeddings",
        "schema": {
            "id": "STRING",
            "content": "STRING",
            "keywords": "ARRAY<STRING>",
            "metadata": "STRUCT<original_id:STRING,type:STRING,primary_category:STRING,location_city:STRING,location_country:STRING,pricing_amount:DOUBLE,pricing_currency:STRING,pricing_for_people:INT,valid_until:STRING,provider_name:STRING,media_url:STRING,coordinates:STRUCT<latitude:DOUBLE,longitude:DOUBLE>>",
            "title": "STRING",
            "description": "STRING", 
            "category": "STRING",
            "location": "STRING",
            "price": "DOUBLE",
            "currency": "STRING",
            "created_at": "TIMESTAMP"
        },
        "vector_search_config": {
            "primary_key": "id",
            "embedding_source_column": "content",
            "embedding_model": "databricks-bge-large-en",
            "sync_mode": "TRIGGERED"
        },
        "hybrid_search_fields": [
            "keywords",
            "title", 
            "category",
            "location"
        ]
    }
    
    return schema_info

def generate_databricks_sql():
    """Generate SQL commands for Databricks setup."""
    
    sql_commands = """
-- Create Delta table for embeddings
CREATE TABLE IF NOT EXISTS experience_embeddings (
    id STRING,
    content STRING,
    keywords ARRAY<STRING>,
    metadata STRUCT<
        original_id: STRING,
        type: STRING,
        primary_category: STRING,
        location_city: STRING,
        location_country: STRING,
        pricing_amount: DOUBLE,
        pricing_currency: STRING,
        pricing_for_people: INT,
        valid_until: STRING,
        provider_name: STRING,
        media_url: STRING,
        coordinates: STRUCT<latitude: DOUBLE, longitude: DOUBLE>
    >,
    title STRING,
    description STRING,
    category STRING,
    location STRING,
    price DOUBLE,
    currency STRING,
    created_at TIMESTAMP
) USING DELTA;

-- Create vector search index for hybrid retrieval
CREATE VECTOR SEARCH INDEX IF NOT EXISTS experience_vector_index
ON experience_embeddings (content)
USING ENDPOINT 'your_vector_search_endpoint'
WITH (
    'embedding_model' = 'databricks-bge-large-en',
    'sync_mode' = 'TRIGGERED'
);

-- Example hybrid search query
-- This combines vector similarity with keyword filtering
SELECT id, title, location, category, price
FROM VECTOR_SEARCH(
    index => 'experience_vector_index',
    query => 'underwater dining experience',
    num_results => 10,
    filters => map(
        'category', 'Culinary',
        'location_country', 'Thailand'
    )
);
"""
    
    return sql_commands

def main():
    """Main execution function."""
    
    # Configuration
    input_file = 'input_new/rag_optimized_flattened_flat.jsonl'
    output_file = 'input_new/mosaic_vector_search_data.jsonl'
    
    # Process data for Mosaic Vector Search
    processed_records = process_for_mosaic_vector_search(input_file, output_file)
    
    # Generate schema and SQL information
    print("\n📊 Generating Databricks configuration...")
    schema_info = create_databricks_schema_info()
    sql_commands = generate_databricks_sql()
    
    # Save configuration files
    with open('input_new/databricks_schema.json', 'w', encoding='utf-8') as f:
        json.dump(schema_info, f, indent=2, ensure_ascii=False)
    
    with open('input_new/databricks_setup.sql', 'w', encoding='utf-8') as f:
        f.write(sql_commands)
    
    print("✅ Configuration files created:")
    print("   - databricks_schema.json")
    print("   - databricks_setup.sql")
    
    print("\n🎯 Next steps for Databricks:")
    print("1. Upload mosaic_vector_search_data.jsonl to DBFS")
    print("2. Create Delta table using databricks_setup.sql")
    print("3. Load data into the table")
    print("4. Create vector search index")
    print("5. Test hybrid search queries")

if __name__ == "__main__":
    main()
